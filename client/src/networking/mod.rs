use anyhow::{Result, anyhow};
use futures_util::{SinkExt, StreamExt};
use tokio::net::TcpStream;
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message, WebSocketStream, MaybeTlsStream};
use std::sync::{Arc, Mutex};
use std::sync::Once;
use std::time::Duration;
use common::{WebSocketMessage, ConnectionStatus};
use tokio::time::interval;
use once_cell::sync::Lazy;

// Singleton WebSocket connection
static INIT: Once = Once::new();
static mut WEBSOCKET: Option<Arc<Mutex<Option<WebSocketStream<MaybeTlsStream<TcpStream>>>>>> = None;
static CONNECTION_STATUS: Lazy<Arc<Mutex<ConnectionStatus>>> = Lazy::new(|| {
    Arc::new(Mutex::new(ConnectionStatus::Disconnected))
});

// Initialize the WebSocket connection
pub async fn init_connection() -> Result<()> {
    // Update connection status
    {
        let mut status = CONNECTION_STATUS.lock().unwrap();
        *status = ConnectionStatus::Connecting;
    }

    // Connect to the WebSocket server
    let url = url::Url::parse("ws://localhost:8080/ws")?;

    match connect_async(url).await {
        Ok((ws_stream, _)) => {
            unsafe {
                INIT.call_once(|| {
                    WEBSOCKET = Some(Arc::new(Mutex::new(Some(ws_stream))));
                });
            }

            // Update connection status
            {
                let mut status = CONNECTION_STATUS.lock().unwrap();
                *status = ConnectionStatus::Connected;
            }

            // Start heartbeat task
            tokio::spawn(async {
                let mut interval = interval(Duration::from_secs(30));
                loop {
                    interval.tick().await;

                    // Send ping message
                    let ping = WebSocketMessage::Ping;
                    if let Ok(serialized) = serde_json::to_string(&ping) {
                        if let Err(e) = send_message(&serialized).await {
                            println!("Failed to send heartbeat: {}", e);
                            break;
                        }
                    }
                }
            });

            Ok(())
        },
        Err(e) => {
            // Update connection status
            {
                let mut status = CONNECTION_STATUS.lock().unwrap();
                *status = ConnectionStatus::Failed;
            }

            Err(anyhow!("Failed to connect: {}", e))
        }
    }
}

// Get the WebSocket connection
fn get_connection() -> Result<Arc<Mutex<Option<WebSocketStream<MaybeTlsStream<TcpStream>>>>>> {
    unsafe {
        if let Some(ws) = &WEBSOCKET {
            Ok(ws.clone())
        } else {
            Err(anyhow!("WebSocket not initialized"))
        }
    }
}

// Get current connection status
pub fn get_connection_status() -> ConnectionStatus {
    let status = CONNECTION_STATUS.lock().unwrap();
    status.clone()
}

// Send a message through the WebSocket
pub async fn send_message(message: &str) -> Result<()> {
    let ws = get_connection()?;
    let mut lock = ws.lock().unwrap();

    if let Some(stream) = &mut *lock {
        stream.send(Message::Text(message.to_string())).await?;
        Ok(())
    } else {
        // Update connection status
        {
            let mut status = CONNECTION_STATUS.lock().unwrap();
            *status = ConnectionStatus::Disconnected;
        }

        Err(anyhow!("WebSocket connection closed"))
    }
}

// Start listening for messages
pub async fn start_message_listener<F>(mut callback: F) -> Result<()>
where
    F: FnMut(String) + Send + 'static,
{
    let ws = get_connection()?;
    let mut lock = ws.lock().unwrap();

    if let Some(stream) = &mut *lock {
        let (write, mut read) = stream.split();

        tokio::spawn(async move {
            while let Some(msg) = read.next().await {
                match msg {
                    Ok(msg) => {
                        match msg {
                            Message::Text(text) => {
                                // Try to parse as a WebSocketMessage
                                match serde_json::from_str::<WebSocketMessage>(&text) {
                                    Ok(ws_msg) => {
                                        // Handle different message types
                                        match ws_msg {
                                            WebSocketMessage::Ping => {
                                                // Respond with Pong
                                                let pong = WebSocketMessage::Pong;
                                                if let Ok(serialized) = serde_json::to_string(&pong) {
                                                    // This is fine to ignore errors here as it's just a heartbeat
                                                    let _ = send_message(&serialized).await;
                                                }
                                            },
                                            WebSocketMessage::Error(err) => {
                                                println!("Server error: {}", err);
                                                callback(format!("Server error: {}", err));
                                            },
                                            _ => {
                                                // Pass other messages to the callback
                                                callback(text);
                                            }
                                        }
                                    },
                                    Err(e) => {
                                        // Not a valid WebSocketMessage, just pass the raw text
                                        println!("Failed to parse message: {}", e);
                                        callback(text);
                                    }
                                }
                            },
                            Message::Close(_) => {
                                // Update connection status
                                {
                                    let mut status = CONNECTION_STATUS.lock().unwrap();
                                    *status = ConnectionStatus::Disconnected;
                                }
                                break;
                            },
                            _ => {} // Ignore other message types
                        }
                    },
                    Err(e) => {
                        println!("WebSocket error: {}", e);
                        // Update connection status
                        {
                            let mut status = CONNECTION_STATUS.lock().unwrap();
                            *status = ConnectionStatus::Failed;
                        }
                        break;
                    }
                }
            }

            println!("WebSocket connection closed");
            // Update connection status
            {
                let mut status = CONNECTION_STATUS.lock().unwrap();
                *status = ConnectionStatus::Disconnected;
            }
        });

        Ok(())
    } else {
        Err(anyhow!("WebSocket connection closed"))
    }
}

// Try to reconnect after connection failure
pub async fn reconnect() -> Result<()> {
    // Close existing connection if any
    unsafe {
        WEBSOCKET = None;
    }

    // Attempt to reconnect
    init_connection().await
}
