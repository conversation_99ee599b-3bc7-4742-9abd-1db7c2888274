use anyhow::Result;
use slint::{VecModel, SharedString, ModelRc};
use std::rc::Rc;

slint::include_modules!();

pub fn init_main_ui() -> Result<MainWindow> {
    let ui = MainWindow::new()?;

    // Initialize empty messages model
    let messages_model = Rc::new(VecModel::<SharedString>::default());
    ui.set_messages(ModelRc::from(messages_model));

    // Initialize channels model
    let channels_model = Rc::new(VecModel::<SharedString>::default());
    ui.set_channels(ModelRc::from(channels_model));

    // Set initial state
    ui.set_username(SharedString::from("User"));
    ui.set_logged_in(false);
    ui.set_connection_status(SharedString::from("Disconnected"));
    ui.set_error_message(SharedString::from(""));

    Ok(ui)
}

pub fn add_message(ui: &MainWindow, sender: &str, content: &str) -> Result<()> {
    let mut messages = ui.get_messages();
    messages.push(SharedString::from(format!("{}: {}", sender, content)));
    ui.set_messages(messages);
    Ok(())
}

pub fn add_channel(ui: &MainWindow, channel_name: &str) -> Result<()> {
    let mut channels = ui.get_channels();

    // Check if channel already exists
    for i in 0..channels.len() {
        if channels.get(i).unwrap().as_str() == channel_name {
            return Ok(());
        }
    }

    channels.push(SharedString::from(channel_name));
    ui.set_channels(channels);
    Ok(())
}

pub fn set_connection_status(ui: &MainWindow, status: &str) -> Result<()> {
    ui.set_connection_status(SharedString::from(status));
    Ok(())
}
