use anyhow::Result;
use slint::{Compo<PERSON><PERSON><PERSON><PERSON>, SharedString, ModelRc, VecModel};
use std::rc::Rc;
use tokio::runtime::Runtime;
use common::{ConnectionStatus, WebSocketMessage, Message as ChatMessage};
use std::sync::{Arc, Mutex};

mod ui;
mod networking;
mod auth;
mod models;

// Create a global runtime for async operations
static RT: once_cell::sync::Lazy<Runtime> = once_cell::sync::Lazy::new(|| {
    tokio::runtime::Builder::new_multi_thread()
        .enable_all()
        .build()
        .expect("Failed to create Tokio runtime")
});

fn main() -> Result<()> {
    // Initialize UI
    let ui = ui::init_main_ui()?;
    let ui_handle = ui.as_weak();

    // Initialize state
    let current_channel = Arc::new(Mutex::new(String::from("general")));
    let connection_status = Arc::new(Mutex::new(ConnectionStatus::Disconnected));

    // Set up channel list
    let channel_model = Rc::new(VecModel::<SharedString>::default());
    channel_model.push(SharedString::from("general"));
    channel_model.push(SharedString::from("random"));
    ui.set_channels(ModelRc::from(channel_model.clone()));

    // Initialize connection status
    ui.set_connection_status(SharedString::from("Disconnected"));

    // Set up handler for channel selection
    ui.on_select_channel(move |channel| {
        let channel_str = channel.to_string();
        let mut current = current_channel.lock().unwrap();
        *current = channel_str.clone();

        println!("Selected channel: {}", channel_str);
    });

    // Set up handler for login
    let login_ui_handle = ui.as_weak();
    ui.on_login(move |username, password| {
        let ui = login_ui_handle.upgrade().unwrap();
        let username_str = username.to_string();

        // Clone for async closure
        let ui_weak = ui.as_weak();
        let conn_status = connection_status.clone();

        RT.spawn(async move {
            // Update connection status
            {
                let mut status = conn_status.lock().unwrap();
                *status = ConnectionStatus::Connecting;
                if let Some(ui) = ui_weak.upgrade() {
                    ui.set_connection_status(SharedString::from("Connecting..."));
                }
            }

            // Attempt to authenticate
            match auth::login(&username_str, &password.to_string()).await {
                Ok(response) => {
                    if response.success {
                        // Initialize websocket connection
                        if let Err(e) = networking::init_connection().await {
                            println!("Failed to connect: {}", e);
                            if let Some(ui) = ui_weak.upgrade() {
                                ui.set_connection_status(SharedString::from("Connection Failed"));
                            }
                            return;
                        }

                        // Set up message listener
                        let ui_weak_clone = ui_weak.clone();
                        if let Err(e) = networking::start_message_listener(move |msg| {
                            if let Some(ui) = ui_weak_clone.upgrade() {
                                let mut messages = ui.get_messages();
                                messages.push(SharedString::from(msg));
                                ui.set_messages(messages);
                            }
                        }).await {
                            println!("Failed to set up message listener: {}", e);
                        }

                        if let Some(ui) = ui_weak.upgrade() {
                            ui.set_username(SharedString::from(username_str));
                            ui.set_connection_status(SharedString::from("Connected"));
                            ui.set_logged_in(true);
                        }

                        let mut status = conn_status.lock().unwrap();
                        *status = ConnectionStatus::Connected;
                    } else {
                        if let Some(ui) = ui_weak.upgrade() {
                            ui.set_error_message(SharedString::from(response.message.unwrap_or_else(||
                                "Login failed".to_string())));
                            ui.set_connection_status(SharedString::from("Disconnected"));
                        }

                        let mut status = conn_status.lock().unwrap();
                        *status = ConnectionStatus::Disconnected;
                    }
                },
                Err(e) => {
                    println!("Login error: {}", e);
                    if let Some(ui) = ui_weak.upgrade() {
                        ui.set_error_message(SharedString::from(format!("Login error: {}", e)));
                        ui.set_connection_status(SharedString::from("Disconnected"));
                    }

                    let mut status = conn_status.lock().unwrap();
                    *status = ConnectionStatus::Failed;
                }
            }
        });
    });

    // Set up handler for send message button
    let send_ui_handle = ui.as_weak();
    let send_channel = current_channel.clone();
    ui.on_send_message(move |message| {
        let ui = send_ui_handle.upgrade().unwrap();
        let message_text = message.trim();

        if !message_text.is_empty() {
            // Get current channel
            let channel_id = send_channel.lock().unwrap().clone();

            // Create a clone for the async closure
            let message_to_send = message_text.to_string();
            let username = ui.get_username().to_string();
            let ui_weak = ui.as_weak();

            // Use the global runtime for async operations
            RT.spawn(async move {
                // Create a proper chat message
                let chat_message = ChatMessage {
                    id: None,
                    content: message_to_send.clone(),
                    sender_id: "self".to_string(),  // This would be the actual user ID in a real app
                    sender_name: username.clone(),
                    channel_id: Some(channel_id),
                    timestamp: std::time::SystemTime::now()
                        .duration_since(std::time::SystemTime::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs(),
                };

                // Create WebSocket message
                let ws_message = WebSocketMessage::ChatMessage(chat_message);

                // Serialize the message
                let serialized = match serde_json::to_string(&ws_message) {
                    Ok(s) => s,
                    Err(e) => {
                        println!("Failed to serialize message: {}", e);
                        return;
                    }
                };

                // Send the message
                if let Err(e) = networking::send_message(&serialized).await {
                    println!("Failed to send message: {}", e);
                    return;
                }

                // Add message to UI (we add this locally without waiting for server echo)
                if let Some(ui) = ui_weak.upgrade() {
                    let mut messages = ui.get_messages();
                    messages.push(SharedString::from(format!("You: {}", message_to_send)));
                    ui.set_messages(messages);
                }
            });
        }

        // Clear the input field
        ui.set_input_text(SharedString::from(""));
    });

    // Start the UI
    ui.run()?;
    Ok(())
}
