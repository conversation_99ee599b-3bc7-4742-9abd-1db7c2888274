# Server Configuration
APP_SERVER__HOST=127.0.0.1
APP_SERVER__PORT=3000
APP_SERVER__CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Database Configuration
# Mode: embedded or remote
APP_DATABASE__MODE=embedded
APP_DATABASE__NAMESPACE=chat_app
APP_DATABASE__DATABASE=chat_db

# For remote mode, uncomment and configure:
# APP_DATABASE__URL=ws://localhost:8000
# APP_DATABASE__USERNAME=root
# APP_DATABASE__PASSWORD=root

# Authentication Configuration
APP_AUTH__JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
APP_AUTH__JWT_EXPIRATION_HOURS=24
APP_AUTH__REFRESH_TOKEN_EXPIRATION_DAYS=30

# Environment
RUN_MODE=development
